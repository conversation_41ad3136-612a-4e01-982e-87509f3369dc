/**
 * Provincial Administration Manager - Public JavaScript
 */

(function($) {
    'use strict';

    // IMMEDIATE print button removal - runs as soon as this script loads
    function immediateRemovePrintButtons() {
        // Use vanilla JavaScript for immediate execution
        const elements = document.querySelectorAll('*');
        elements.forEach(function(el) {
            const text = (el.textContent || '').toLowerCase();
            const innerHTML = (el.innerHTML || '').toLowerCase();
            const onclick = el.getAttribute('onclick') || '';
            const className = el.getAttribute('class') || '';
            const id = el.getAttribute('id') || '';

            // Check for print-related content
            if (text.includes('print') || text.includes('🖨️') ||
                innerHTML.includes('print') || innerHTML.includes('🖨️') ||
                onclick.toLowerCase().includes('print') ||
                className.toLowerCase().includes('print') ||
                id.toLowerCase().includes('print')) {

                // Completely remove the element
                el.style.display = 'none !important';
                el.style.visibility = 'hidden !important';
                el.style.opacity = '0 !important';
                el.style.position = 'absolute !important';
                el.style.left = '-9999px !important';
                el.style.top = '-9999px !important';
                el.style.width = '0 !important';
                el.style.height = '0 !important';
                el.style.overflow = 'hidden !important';
                el.style.pointerEvents = 'none !important';
                el.style.zIndex = '-9999 !important';

                // Also try to remove from DOM
                try {
                    if (el.parentNode) {
                        el.parentNode.removeChild(el);
                    }
                } catch(e) {
                    // Ignore errors
                }
            }
        });

        // jQuery version if available
        if (typeof $ !== 'undefined') {
            $('*').each(function() {
                const $el = $(this);
                const text = $el.text().toLowerCase();
                const html = $el.html().toLowerCase();
                const onclick = $el.attr('onclick') || '';
                const className = $el.attr('class') || '';
                const id = $el.attr('id') || '';

                if (text.includes('print') || text.includes('🖨️') ||
                    html.includes('print') || html.includes('🖨️') ||
                    onclick.toLowerCase().includes('print') ||
                    className.toLowerCase().includes('print') ||
                    id.toLowerCase().includes('print')) {

                    $el.css({
                        'display': 'none !important',
                        'visibility': 'hidden !important',
                        'opacity': '0 !important',
                        'position': 'absolute !important',
                        'left': '-9999px !important',
                        'top': '-9999px !important',
                        'width': '0 !important',
                        'height': '0 !important',
                        'overflow': 'hidden !important',
                        'pointer-events': 'none !important',
                        'z-index': '-9999 !important'
                    });

                    try {
                        $el.remove();
                    } catch(e) {
                        // Ignore errors
                    }
                }
            });
        }
    }

    // Run immediately
    immediateRemovePrintButtons();

    // Run again when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', immediateRemovePrintButtons);
    } else {
        immediateRemovePrintButtons();
    }

    // SUPER AGGRESSIVE: Run every 100ms for the first 10 seconds
    let superAggressiveCount = 0;
    const superAggressiveInterval = setInterval(function() {
        immediateRemovePrintButtons();

        // Also target specific elements that look like the print buttons in the screenshot
        const suspiciousElements = document.querySelectorAll('*');
        suspiciousElements.forEach(function(el) {
            const computedStyle = window.getComputedStyle(el);
            const text = (el.textContent || '').trim().toLowerCase();
            const innerHTML = (el.innerHTML || '').toLowerCase();

            // Target elements that look like print buttons
            if ((text === 'print' || text.includes('🖨️') || innerHTML.includes('print')) &&
                (el.tagName === 'BUTTON' || el.tagName === 'A' || el.tagName === 'INPUT' ||
                 el.tagName === 'SPAN' || el.tagName === 'DIV')) {

                // Nuclear removal
                el.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; position: absolute !important; left: -9999px !important; top: -9999px !important; width: 0 !important; height: 0 !important; overflow: hidden !important; pointer-events: none !important; z-index: -9999 !important;';

                try {
                    el.remove();
                } catch(e) {}
            }
        });

        superAggressiveCount++;
        if (superAggressiveCount >= 100) { // Stop after 10 seconds (100 * 100ms)
            clearInterval(superAggressiveInterval);
        }
    }, 100);

    // Inject aggressive CSS immediately
    const aggressiveStyle = document.createElement('style');
    aggressiveStyle.type = 'text/css';
    aggressiveStyle.innerHTML = `
        /* IMMEDIATE PRINT BUTTON KILLER */
        *[class*="print" i],
        *[id*="print" i],
        button:contains("Print"),
        button:contains("🖨️"),
        a:contains("Print"),
        a:contains("🖨️"),
        span:contains("Print"),
        span:contains("🖨️"),
        div:contains("Print"),
        div:contains("🖨️"),
        *[onclick*="print" i],
        *[href*="print" i],
        *[title*="print" i],
        *[aria-label*="print" i],
        *[alt*="print" i] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            position: absolute !important;
            left: -9999px !important;
            top: -9999px !important;
            width: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            pointer-events: none !important;
            z-index: -9999 !important;
            max-width: 0 !important;
            max-height: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            background: transparent !important;
            color: transparent !important;
            font-size: 0 !important;
            line-height: 0 !important;
        }
    `;

    // Insert the style immediately
    if (document.head) {
        document.head.appendChild(aggressiveStyle);
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            document.head.appendChild(aggressiveStyle);
        });
    }

    // Enhanced Slideshow functionality
    const slideshowInstances = {};

    // Initialize all slideshows on page
    function initSlideshows() {
        $('.esp-slideshow, .esp-slideshow-section').each(function() {
            const slideshowId = $(this).attr('id');
            if (slideshowId) {
                initSingleSlideshow(slideshowId);
            }
        });

        // Initialize legacy slideshow if present
        if ($('.esp-slideshow-section').length > 0 && !$('.esp-slideshow-section').attr('id')) {
            initLegacySlideshow();
        }
    }

    // Initialize a single slideshow instance
    function initSingleSlideshow(slideshowId) {
        const $slideshow = $('#' + slideshowId);
        const $slides = $slideshow.find('.esp-slide');
        const $dots = $slideshow.find('.esp-dot, .esp-slide-dot');

        if ($slides.length === 0) return;

        const autoplay = $slideshow.data('autoplay') !== false && $slideshow.data('autoplay') !== 'false';
        const duration = parseInt($slideshow.data('duration')) || 5000;

        const instance = {
            currentSlide: 0,
            totalSlides: $slides.length,
            autoplay: autoplay,
            duration: duration,
            intervalId: null,
            isPaused: false
        };

        slideshowInstances[slideshowId] = instance;

        // Show first slide
        showSlide(slideshowId, 0);

        // Start autoplay if enabled
        if (autoplay) {
            startAutoplay(slideshowId);
        }

        // Pause on hover
        $slideshow.on('mouseenter', function() {
            pauseSlideshow(slideshowId);
        }).on('mouseleave', function() {
            if (autoplay) {
                resumeSlideshow(slideshowId);
            }
        });

        // Touch/swipe support
        let startX = 0;
        let endX = 0;

        $slideshow.on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $slideshow.on('touchend', function(e) {
            endX = e.originalEvent.changedTouches[0].clientX;
            handleSwipe(slideshowId, startX, endX);
        });
    }

    // Show specific slide
    function showSlide(slideshowId, slideIndex) {
        const instance = slideshowInstances[slideshowId];
        if (!instance) return;

        const $slideshow = $('#' + slideshowId);
        const $slides = $slideshow.find('.esp-slide');
        const $dots = $slideshow.find('.esp-dot, .esp-slide-dot');

        // Wrap around
        if (slideIndex >= instance.totalSlides) slideIndex = 0;
        if (slideIndex < 0) slideIndex = instance.totalSlides - 1;

        instance.currentSlide = slideIndex;

        // Update slides
        $slides.removeClass('active');
        $slides.eq(slideIndex).addClass('active');

        // Update dots
        $dots.removeClass('active');
        $dots.eq(slideIndex).addClass('active');
    }

    // Start autoplay
    function startAutoplay(slideshowId) {
        const instance = slideshowInstances[slideshowId];
        if (!instance || instance.intervalId) return;

        instance.intervalId = setInterval(function() {
            if (!instance.isPaused) {
                nextSlide(slideshowId);
            }
        }, instance.duration);
    }

    // Stop autoplay
    function stopAutoplay(slideshowId) {
        const instance = slideshowInstances[slideshowId];
        if (!instance || !instance.intervalId) return;

        clearInterval(instance.intervalId);
        instance.intervalId = null;
    }

    // Pause slideshow
    function pauseSlideshow(slideshowId) {
        const instance = slideshowInstances[slideshowId];
        if (instance) {
            instance.isPaused = true;
        }
    }

    // Resume slideshow
    function resumeSlideshow(slideshowId) {
        const instance = slideshowInstances[slideshowId];
        if (instance) {
            instance.isPaused = false;
        }
    }

    // Next slide
    function nextSlide(slideshowId) {
        const instance = slideshowInstances[slideshowId];
        if (instance) {
            showSlide(slideshowId, instance.currentSlide + 1);
        }
    }

    // Previous slide
    function prevSlide(slideshowId) {
        const instance = slideshowInstances[slideshowId];
        if (instance) {
            showSlide(slideshowId, instance.currentSlide - 1);
        }
    }

    // Handle swipe gestures
    function handleSwipe(slideshowId, startX, endX) {
        const threshold = 50;
        const diff = startX - endX;

        if (Math.abs(diff) > threshold) {
            if (diff > 0) {
                nextSlide(slideshowId); // Swipe left - next slide
            } else {
                prevSlide(slideshowId); // Swipe right - previous slide
            }
        }
    }

    // Legacy slideshow support
    let espSlideIndex = 1;

    function initLegacySlideshow() {
        const $slideshow = $('.esp-slideshow-section');
        if ($slideshow.length === 0) return;

        espShowSlides(espSlideIndex);

        // Auto-advance slides every 5 seconds
        setInterval(espAutoSlides, 5000);

        // Keyboard navigation
        $(document).on('keydown', function(e) {
            if ($slideshow.is(':visible')) {
                if (e.keyCode === 37) { // Left arrow
                    espCurrentSlide(espSlideIndex - 1);
                } else if (e.keyCode === 39) { // Right arrow
                    espCurrentSlide(espSlideIndex + 1);
                }
            }
        });
    }

    function espShowSlides(n) {
        let slides = $('.esp-slideshow-section .esp-slide');
        let dots = $('.esp-slideshow-section .esp-slide-dot');

        if (n > slides.length) { espSlideIndex = 1; }
        if (n < 1) { espSlideIndex = slides.length; }

        slides.removeClass('active');
        dots.removeClass('active');

        if (slides.length > 0) {
            slides.eq(espSlideIndex - 1).addClass('active');
            dots.eq(espSlideIndex - 1).addClass('active');
        }
    }

    function espAutoSlides() {
        espSlideIndex++;
        espShowSlides(espSlideIndex);
    }

    // Global functions for slideshow navigation
    window.espSlideshowNext = function(slideshowId) {
        nextSlide(slideshowId);
    };

    window.espSlideshowPrev = function(slideshowId) {
        prevSlide(slideshowId);
    };

    window.espSlideshowGoTo = function(slideshowId, slideIndex) {
        showSlide(slideshowId, slideIndex);
    };

    // Legacy function
    window.espCurrentSlide = function(n) {
        espShowSlides(espSlideIndex = n);
    };
    
    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            let target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
    }
    
    // Animation on scroll
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    $(entry.target).addClass('animate-in');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        $('.esp-mp-card, .esp-district-card, .esp-contact-card').each(function() {
            observer.observe(this);
        });
    }
    
    // Lazy loading for images
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            $('.esp-lazy-image').each(function() {
                imageObserver.observe(this);
            });
        }
    }
    
    // Statistics counter animation
    function initCounterAnimation() {
        $('.esp-map-stat-number').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.text().replace(/,/g, ''));
            
            if (!isNaN(countTo)) {
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.floor(this.countNum).toLocaleString());
                    },
                    complete: function() {
                        $this.text(countTo.toLocaleString());
                    }
                });
            }
        });
    }
    
    // Responsive table handling
    function initResponsiveTables() {
        $('.esp-list-table').each(function() {
            if (!$(this).parent().hasClass('table-responsive')) {
                $(this).wrap('<div class="table-responsive"></div>');
            }
        });
    }
    
    // Card hover effects
    function initCardEffects() {
        $('.esp-mp-card, .esp-district-card').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );
    }
    
    // Search functionality for lists - REMOVED
    // MP search functionality has been removed as requested
    

    
    // Accessibility improvements
    function initAccessibility() {
        // Add ARIA labels
        $('.esp-slide-dot').attr('role', 'button').attr('aria-label', 'Go to slide');
        $('.esp-mp-card').attr('role', 'article');
        $('.esp-district-card').attr('role', 'article');
        
        // Keyboard navigation for cards
        $('.esp-mp-card, .esp-district-card').attr('tabindex', '0').on('keydown', function(e) {
            if (e.keyCode === 13 || e.keyCode === 32) { // Enter or Space
                $(this).click();
            }
        });
        
        // Focus management for slideshow
        $('.esp-slide-dot').on('focus', function() {
            $(this).addClass('focused');
        }).on('blur', function() {
            $(this).removeClass('focused');
        });
    }
    
    // Error handling for missing images
    function initImageErrorHandling() {
        $('img').on('error', function() {
            const $this = $(this);
            if ($this.closest('.esp-governor-photo').length) {
                $this.parent().html('<div class="esp-placeholder-photo">👤</div>');
            } else if ($this.closest('.esp-mp-photo').length) {
                $this.parent().html('👤');
            }
        });
    }

    // Map functionality
    function initMaps() {
        console.log('ESP Maps: Initializing maps...');

        // Check if Leaflet is available
        if (typeof L === 'undefined') {
            console.error('ESP Maps: Leaflet library not loaded!');
            $('.esp-map-display').each(function() {
                $(this).html(`
                    <div class="esp-map-error">
                        <h4>Map Library Not Available</h4>
                        <p>The mapping library failed to load. Please check your internet connection and refresh the page.</p>
                        <p><small>Debug: Leaflet.js not found</small></p>
                    </div>
                `);
            });
            return;
        }

        console.log('ESP Maps: Leaflet library loaded successfully');

        // Initialize all map containers
        $('.esp-map-display').each(function() {
            const mapContainer = $(this);
            const mapId = mapContainer.attr('id');
            const jsonUrl = mapContainer.data('json-url');
            const mapTitle = mapContainer.data('map-title');

            console.log('ESP Maps: Processing map container:', {
                mapId: mapId,
                jsonUrl: jsonUrl,
                mapTitle: mapTitle
            });

            if (!jsonUrl) {
                console.error('ESP Maps: No JSON URL found for map:', mapId);
                mapContainer.html('<div class="esp-map-error">No map data available</div>');
                return;
            }

            // Check if map container is already initialized
            if (mapContainer[0]._leaflet_id) {
                console.log('ESP Maps: Map already initialized for:', mapId, '- skipping');
                return;
            }

            // Initialize Leaflet map
            try {
                console.log('ESP Maps: Initializing Leaflet map for:', mapId);

                const map = L.map(mapId, {
                    zoomControl: true,
                    scrollWheelZoom: true,
                    doubleClickZoom: true,
                    boxZoom: true,
                    keyboard: true,
                    dragging: true,
                    touchZoom: true
                });

                console.log('ESP Maps: Leaflet map created successfully');

                // Add OpenStreetMap tile layer
                console.log('ESP Maps: Adding tile layer...');
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18,
                    minZoom: 2
                }).addTo(map);

                console.log('ESP Maps: Tile layer added, fetching JSON data from:', jsonUrl);

                // Load and display GeoJSON data
                fetch(jsonUrl)
                    .then(response => {
                        console.log('ESP Maps: Fetch response received:', response.status, response.statusText);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(geoJsonData => {
                        console.log('ESP Maps: JSON data parsed successfully:', {
                            type: geoJsonData.type,
                            features: geoJsonData.features ? geoJsonData.features.length : 0,
                            crs: geoJsonData.crs
                        });

                        // Add GeoJSON layer to map
                        const geoJsonLayer = L.geoJSON(geoJsonData, {
                            style: function(feature) {
                                return {
                                    fillColor: '#006A4E', // PNG Green
                                    weight: 2,
                                    opacity: 1,
                                    color: '#CE1126', // PNG Red for borders
                                    dashArray: '3',
                                    fillOpacity: 0.3
                                };
                            },
                            onEachFeature: function(feature, layer) {
                                // Add popup with feature information
                                if (feature.properties) {
                                    let popupContent = '<div class="esp-map-popup">';

                                    if (feature.properties.PROVNAME) {
                                        popupContent += `<h4>${feature.properties.PROVNAME}</h4>`;
                                    }

                                    if (feature.properties.PROVID) {
                                        popupContent += `<p><strong>Province ID:</strong> ${feature.properties.PROVID}</p>`;
                                    }

                                    if (feature.properties.FID) {
                                        popupContent += `<p><strong>Feature ID:</strong> ${feature.properties.FID}</p>`;
                                    }

                                    popupContent += '</div>';
                                    layer.bindPopup(popupContent);
                                }

                                // Add hover effects
                                layer.on({
                                    mouseover: function(e) {
                                        const layer = e.target;
                                        layer.setStyle({
                                            weight: 3,
                                            color: '#FFD700', // PNG Yellow on hover
                                            dashArray: '',
                                            fillOpacity: 0.5
                                        });
                                        layer.bringToFront();
                                    },
                                    mouseout: function(e) {
                                        geoJsonLayer.resetStyle(e.target);
                                    }
                                });
                            }
                        }).addTo(map);

                        // Fit map to bounds of the GeoJSON data
                        if (geoJsonLayer.getBounds().isValid()) {
                            map.fitBounds(geoJsonLayer.getBounds(), {
                                padding: [20, 20]
                            });
                        }

                        // Add map title as control
                        if (mapTitle) {
                            const titleControl = L.control({position: 'topright'});
                            titleControl.onAdd = function(map) {
                                const div = L.DomUtil.create('div', 'esp-map-title-control');
                                div.innerHTML = `<h5>${mapTitle}</h5>`;
                                div.style.background = 'rgba(255, 255, 255, 0.9)';
                                div.style.padding = '10px';
                                div.style.borderRadius = '5px';
                                div.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                                return div;
                            };
                            titleControl.addTo(map);
                        }

                        // Remove loading indicator
                        mapContainer.find('.esp-map-loading').fadeOut();

                        console.log(`Map ${mapId} initialized successfully with ${geoJsonData.features ? geoJsonData.features.length : 0} features`);

                    })
                    .catch(error => {
                        console.error('ESP Maps: Error loading map data:', error);
                        console.error('ESP Maps: JSON URL was:', jsonUrl);
                        mapContainer.html(`
                            <div class="esp-map-error">
                                <h4>Error Loading Map</h4>
                                <p>Unable to load map data. Please check the JSON file.</p>
                                <p><strong>JSON URL:</strong> <a href="${jsonUrl}" target="_blank">${jsonUrl}</a></p>
                                <p><small>Error: ${error.message}</small></p>
                                <button onclick="window.location.reload()" class="button">Retry</button>
                            </div>
                        `);
                    });

            } catch (error) {
                console.error('Error initializing map:', error);
                mapContainer.html(`
                    <div class="esp-map-error">
                        <h4>Map Initialization Error</h4>
                        <p>Unable to initialize the map. Please check your browser console for details.</p>
                    </div>
                `);
            }
        });
    }

    // Debug function for testing
    window.espDebugMaps = function() {
        console.log('ESP Maps Debug Info:');
        console.log('- jQuery loaded:', typeof $ !== 'undefined');
        console.log('- Leaflet loaded:', typeof L !== 'undefined');
        console.log('- Map containers found:', $('.esp-map-display').length);

        $('.esp-map-display').each(function(index) {
            const container = $(this);
            console.log(`Map ${index + 1}:`, {
                id: container.attr('id'),
                jsonUrl: container.data('json-url'),
                title: container.data('map-title')
            });
        });

        // Try to reinitialize maps
        console.log('Attempting to reinitialize maps...');
        initMaps();
    };

    // AGGRESSIVE print button removal function
    function removePrintButtons() {
        console.log('ESP: Removing print buttons...');

        // Remove all possible print button selectors
        const printSelectors = [
            'button[onclick*="print"]',
            'button[onclick*="Print"]',
            'a[onclick*="print"]',
            'a[onclick*="Print"]',
            '.print-button',
            '.print-btn',
            '.esp-print-btn',
            '[class*="print-button"]',
            '[class*="print-btn"]',
            'input[value*="Print"]',
            'input[value*="print"]',
            'button:contains("Print")',
            'button:contains("🖨️")',
            'a:contains("Print")',
            'a:contains("🖨️")'
        ];

        printSelectors.forEach(function(selector) {
            try {
                $(selector).remove();
            } catch(e) {
                // Ignore selector errors
            }
        });

        // Specifically target our sections with extreme prejudice
        $('.esp-governor-section, .esp-parliament-section, .esp-districts-section').each(function() {
            const $section = $(this);

            // Remove any element that might be a print button
            $section.find('*').each(function() {
                const $el = $(this);
                const text = $el.text().toLowerCase();
                const html = $el.html().toLowerCase();
                const onclick = $el.attr('onclick') || '';
                const className = $el.attr('class') || '';
                const id = $el.attr('id') || '';

                if (text.includes('print') ||
                    text.includes('🖨️') ||
                    html.includes('print') ||
                    html.includes('🖨️') ||
                    onclick.toLowerCase().includes('print') ||
                    className.toLowerCase().includes('print') ||
                    id.toLowerCase().includes('print')) {

                    if ($el.is('button, a, input, span, div')) {
                        console.log('ESP: Removing print element:', $el[0]);
                        $el.remove();
                    }
                }
            });
        });

        // Nuclear option: remove any element containing print-related content
        $('button, a, input').each(function() {
            const $el = $(this);
            const text = $el.text().toLowerCase();
            const onclick = $el.attr('onclick') || '';

            if ((text.includes('print') || text.includes('🖨️') || onclick.toLowerCase().includes('print')) &&
                !text.includes('footprint') && !text.includes('blueprint') && !text.includes('imprint')) {
                console.log('ESP: Nuclear removal of print element:', $el[0]);
                $el.remove();
            }
        });
    }

    // Initialize all functionality when document is ready
    $(document).ready(function() {
        console.log('ESP: Document ready, initializing...');
        initSlideshows();
        initSmoothScrolling();
        initScrollAnimations();
        initLazyLoading();
        initResponsiveTables();
        initCardEffects();
        initAccessibility();
        initImageErrorHandling();

        // Remove print buttons
        removePrintButtons();

        // Set up observer to remove print buttons that might be added dynamically
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    removePrintButtons();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // CONTINUOUS monitoring - run every 500ms for first 10 seconds, then every 2 seconds
        let removalCount = 0;
        const intensiveInterval = setInterval(function() {
            removePrintButtons();
            immediateRemovePrintButtons();
            removalCount++;
            if (removalCount >= 20) { // Stop intensive after 10 seconds (20 * 500ms)
                clearInterval(intensiveInterval);

                // Continue with less frequent monitoring
                setInterval(function() {
                    removePrintButtons();
                    immediateRemovePrintButtons();
                }, 2000); // Every 2 seconds indefinitely
            }
        }, 500);

        // Add a small delay for maps to ensure all dependencies are loaded
        setTimeout(function() {
            console.log('ESP: Initializing maps after delay...');
            initMaps();
        }, 1000);
        
        // Trigger counter animation when statistics section is visible
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    initCounterAnimation();
                    statsObserver.unobserve(entry.target);
                }
            });
        });
        
        if ($('.esp-statistics-section').length) {
            statsObserver.observe($('.esp-statistics-section')[0]);
        }
    });
    
    // Handle window resize
    $(window).on('resize', function() {
        // Recalculate slideshow dimensions if needed
        if ($('.esp-slideshow-section').length > 0) {
            // Slideshow responsive adjustments
        }
    });
    
    // Handle window load
    $(window).on('load', function() {
        // Add loading animations
        $('.esp-mp-card, .esp-district-card, .esp-event-item, .esp-news-item').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
    });

})(jQuery);
